"""
Test script for dashboard API changes.

This script tests the new dashboard API endpoints and functions.
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.repositories.chatbi.dashboard.statistics import get_dashboard_stats
from src.repositories.chatbi.dashboard.users import get_top_users, get_top_agents


def test_dashboard_stats():
    """Test the dashboard statistics function."""
    print("\n=== Testing Dashboard Statistics ===")
    
    # Test with no parameters
    stats = get_dashboard_stats()
    print("Default stats:", stats)
    
    # Test with time range
    now = datetime.now()
    end_time = int(now.timestamp() * 1000)
    start_time = int((now - timedelta(days=7)).timestamp() * 1000)
    
    stats_with_time = get_dashboard_stats(start_time, end_time)
    print(f"Stats with time range (last 7 days):", stats_with_time)
    
    # Test with admin filter
    stats_with_admin_filter = get_dashboard_stats(filter_admin=True)
    print("Stats with admin filter:", stats_with_admin_filter)
    
    # Test with time range and admin filter
    stats_with_time_and_filter = get_dashboard_stats(start_time, end_time, True)
    print("Stats with time range and admin filter:", stats_with_time_and_filter)

def test_top_users():
    """Test the top users function."""
    print("\n=== Testing Top Users ===")
    
    # Test with default parameters
    top_users = get_top_users()
    print(f"Top 10 users:", top_users)
    
    # Test with limit
    top_5_users = get_top_users(limit=5)
    print(f"Top 5 users:", top_5_users)
    
    # Test with time range
    now = datetime.now()
    end_time = int(now.timestamp() * 1000)
    start_time = int((now - timedelta(days=30)).timestamp() * 1000)
    
    top_users_with_time = get_top_users(limit=5, start_time=start_time, end_time=end_time)
    print(f"Top 5 users in last 30 days:", top_users_with_time)
    
    # Test with admin filter
    top_users_with_filter = get_top_users(limit=5, filter_admin=True)
    print(f"Top 5 users (excluding admins):", top_users_with_filter)

def test_top_agents():
    """Test the top agents function."""
    print("\n=== Testing Top Agents ===")
    
    # Test with default parameters
    top_agents = get_top_agents()
    print(f"Top 10 agents:", top_agents)
    
    # Test with limit
    top_5_agents = get_top_agents(limit=5)
    print(f"Top 5 agents:", top_5_agents)
    
    # Test with time range
    now = datetime.now()
    end_time = int(now.timestamp() * 1000)
    start_time = int((now - timedelta(days=30)).timestamp() * 1000)
    
    top_agents_with_time = get_top_agents(limit=5, start_time=start_time, end_time=end_time)
    print(f"Top 5 agents in last 30 days:", top_agents_with_time)
    
    # Test with admin filter
    top_agents_with_filter = get_top_agents(limit=5, filter_admin=True)
    print(f"Top 5 agents (excluding admins):", top_agents_with_filter)

if __name__ == "__main__":
    test_dashboard_stats()
    test_top_users()
    test_top_agents()
