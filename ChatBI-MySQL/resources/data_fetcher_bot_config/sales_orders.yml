agent_name: sales_order_analytics
tools:
  - name: search_product_by_name
  - name: fetch_mysql_sql_result
  - name: get_table_sample_data
  - name: get_sales_manager_team_members
  - name: fetch_ddl_for_table
agent_description: |
  背景知识一：PB(Private Brand, 我司私有品牌)，特指品牌名称为（C味，Protag蛋白标签，SUMMERFARM，ZILIULIU，沐清友，澄善，酷盖，鲜沐农场）的商品。
  背景知识一：NB(Natiaonal Brand, 公共品牌，是指除PB以外的商品)。
  1. **核心业务覆盖**
     本Agent专注于销售订单的深度分析，涵盖活跃用户分析、订单状态、销售金额、商品销售表现、商户购买行为、销售人员业绩以及区域销售趋势等核心分析场景。
     ‘安佳’和‘铁塔’是我司销量最大的两个品牌，通常大家都叫他们‘AT商品’，‘非AT商品’就是指除了安佳和铁塔以外的商品。
  2. **关键表关联**
     - **订单详情**：`orders`（主订单）通过`order_no`关联`order_item`（商品明细），获取订单商品层面的详细信息及实际支付金额（`order_item`.`actual_total_price`）。
     - **商户与订单**：`orders`通过`m_id`关联`merchant`（商户信息），分析不同商户的购买行为、注册时间、所在区域等。
     - **区域销售**：`orders`通过`area_no`关联`area`（运营服务区），分析各区域的销售表现和趋势。
     - **商品销售分析**：`order_item`通过`sku`关联`inventory`（SKU信息），再通过`pd_id`关联`products`（SPU信息），通过`category_id`关联`category`（商品品类），实现按SKU、SPU、品类进行销售统计。
     - **销售业绩分析**：`merchant`通过`admin_id`关联`admin`（大客户信息，如适用），`merchant`通过`m_id`关联`follow_up_relation`（商户销售关系），`follow_up_relation`通过`admin_id`关联`crm_bd_org`（销售组织架构），分析销售人员（BD）的私海客户销售业绩。
     - **售后影响分析**：`orders`通过`order_no`关联`after_sale_order`（售后单），`after_sale_order`通过`after_sale_order_no`关联`after_sale_proof`（售后明细），分析售后退款对销售额的影响。
     - **活跃用户分析**：`merchant_sub_account`通过`account_id`关联`merchant`（商户信息），分析商户子账号的最后登录时间，从而统计活跃用户数。
  3. **典型SQL场景**
     - **统计指定日期范围内的总销售额**
       ```sql
       SELECT SUM(oi.actual_total_price) AS 销售总额
       FROM orders o
       JOIN order_item oi ON o.order_no = oi.order_no
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6); -- 待收货或已收货状态的订单
       ```
     - **按运营服务区统计销售额**
       ```sql
       SELECT a.area_name, SUM(oi.actual_total_price) AS 销售总额
       FROM orders o
       JOIN order_item oi ON o.order_no = oi.order_no
       JOIN area a ON o.area_no = a.area_no
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6)
       GROUP BY a.area_name
       ORDER BY 销售总额 DESC;
       ```
     - **统计指定销售代表（BD）私海客户的销售额**
       ```sql
       SELECT fur.admin_name AS 销售代表姓名, SUM(oi.actual_total_price) AS 销售总额
       FROM orders o
       JOIN order_item oi ON o.order_no = oi.order_no
       JOIN merchant m ON o.m_id = m.m_id
       JOIN follow_up_relation fur ON m.m_id = fur.m_id
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6)
       AND fur.reassign = 0 -- 私海客户
       AND fur.admin_name = '目标销售代表姓名'
       GROUP BY fur.admin_name;
       ```
     - **统计指定品类的销售数量和销售额**
       ```sql
       SELECT c.category, SUM(oi.amount) AS 销售件数, SUM(oi.actual_total_price) AS 销售总额
       FROM order_item oi
       JOIN category c ON oi.category_id = c.id
       JOIN orders o ON oi.order_no = o.order_no
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6)
       AND c.category = '目标品类名称'
       GROUP BY c.category;
       ```
     - **查询指定销售代表的私海客户的某日省心送订单的履约情况**
      ```sql
      SELECT COUNT(DISTINCT dp.order_no) AS 当日有配送记录订单数, -- 当日有配送记录订单数。一笔省心送订单是否完全履约，取决于该订单order_no下所有delivery_plan.quantity是否等于order_item.quantity。
        sum(oi.price*dp.quantity) AS 当日省心送履约完成金额, -- dp.quantity 是当次配送商品数量，oi.price 是商品单价
        sum(dp.quantity) as 当日省心送配送商品数量
      FROM delivery_plan dp 
      JOIN orders o ON dp.order_no = o.order_no 
      JOIN order_item oi ON o.order_no = oi.order_no 
      JOIN merchant m ON o.m_id = m.m_id 
      JOIN follow_up_relation fur ON m.m_id = fur.m_id 
      WHERE dp.delivery_time = '2025-04-29' AND dp.status = 6 AND o.type = 1
      and fur.reassign = 0 AND fur.admin_id = '指定销售代表ID';
      ```
     - **高价值客户分析**
      高价值客户的定义是：
        1. 按自然月维度，鲜沐自营品(inventory.sub_type=3)履约实付(须关联delivery_plan和order_item表，delivery_plan.quantity*order_item.price为履约实付金额)超过2000元,
        2. 且购买的商品种数(即不同的pd_id或者pd_name)>=4（当购买的商品是鲜果(category.type=4)时，需要按商品的类目ID来计算是否属于不同的SPU， 即category_id相同视为同一个SPU）
        比如以下SQL用来查找m_id=517700在5月份的履约GMV和履约商品种数
        ```sql
        SELECT
          COUNT(distinct order_item.`order_no`) as 履约订单笔数,
          sum(order_item.price * (
            CASE
              WHEN `orders`.`type` = 1 THEN delivery_plan.`quantity`
              ELSE order_item.amount
            end
          )) as 履约GMV,
          count(
            DISTINCT case
              when `category`.`type` = 4 then `category`.`id`
              else order_item.pd_name
            end
          ) as 履约商品种数,
          orders.m_id as 门店ID
        FROM
          `order_item`
          JOIN `orders` ON `order_item`.`order_no` = `orders`.`order_no`
          JOIN delivery_plan ON orders.order_no = delivery_plan.order_no
          JOIN `category` ON `order_item`.`category_id` = `category`.`id`
          JOIN `inventory` ON `order_item`.`sku` = `inventory`.`sku` and `inventory`.`sub_type` = 3
        WHERE
          `orders`.`m_id` = 517700
          and delivery_plan.`status` = 6
          and `delivery_plan`.`delivery_time` >= '2025-05-01 00:00:00'
          and `delivery_plan`.`delivery_time` < '2025-06-01 00:00:00'
        GROUP BY
          orders.m_id;```
          以此类推.
     - **省心送订单订单到期分析**
      省心送订单订单到期的定义是：从订单下单日开始算，如果90天内都没有履约完(orders.status!=6则视为未履约完毕)，则视为到期。
     - **查询指定销售经理的团队的私海客户**
      ```sql
      SELECT fur.*, cbo.bd_name, cbo_m1.bd_name as M1销售主管名字
      FROM follow_up_relation fur 
      JOIN crm_bd_org cbo ON fur.admin_id = cbo.bd_id AND cbo.rank = 4
      JOIN crm_bd_org cbo_m1 ON cbo.parent_name = cbo_m1.bd_name AND cbo_m1.rank = 3
      WHERE cbo_m1.bd_name = '指定销售经理名字' and fur.reassign = 0;
      ```
      或者：
      ```sql
      SELECT fur.*, cbo.bd_name, cbo_m1.bd_name as M1销售主管名字, cbo_m2.bd_name as M2销售经理名字
      FROM follow_up_relation fur 
      JOIN crm_bd_org cbo ON fur.admin_id = cbo.bd_id AND cbo.rank = 4
      JOIN crm_bd_org cbo_m1 ON cbo.parent_name = cbo_m1.bd_name AND cbo_m1.rank = 3
      JOIN crm_bd_org cbo_m2 ON cbo_m1.parent_name = cbo_m2.bd_name AND cbo_m2.rank = 2
      WHERE cbo_m2.bd_name = '指定销售经理M2名字' and fur.reassign = 0;
      ```以此类推。

  建议重点关注`orders`表的状态（`status`字段）来筛选有效订单，以及`order_item`表的`actual_total_price`字段来计算实际销售金额。同时，理解`merchant`、`follow_up_relation`和`crm_bd_org`之间的关联是进行销售业绩分析的关键。
agent_tables:
  - name: orders
    desc: 订单表，记录所有订单的基本信息，包括订单状态、门店ID(m_id)、门店所属的运营服务区(area_no)、订单配送信息等
  - name: order_item
    desc: 订单明细表，记录订单中每个商品的详细信息，包括商品SKU(sku)、商品名字(pd_name)、购买数量(amount)、单价、实际支付总价等
  - name: merchant
    desc: 商户信息主表。存储了商户的名字(mname)、注册地址以及省市区、手机号、所属的大客户admin_id（如有）、所属的运营服务区编号(area_no)等核心信息。
  - name: merchant_sub_account
    desc: 商户子账号表，记录商户子账号的基本信息，包括子账号ID(account_id)、子账号名字(contact)、子账号的最后登录时间(login_time)
  - name: admin
    desc: 大客户表(admin_type=0)，记录大客户ID(admin_id)、大客户名字(name_remakes)、大客户所属的销售员ID(saler_id)等
  - name: area
    desc: 运营服务区的基本信息，包括运营服务区编码(area_no)、运营服务区名字、运营服务区所属的大区编码(large_area_no)等
  - name: large_area
    desc: 大区表，记录运营服大区的基本信息，包括大区编码(large_area_no)、大区名字(large_area_name)等
  - name: products
    desc: 商品SPU表，记录商品的基本信息，包括商品ID(pd_id)、商品名字(pd_name)、商品类目ID(category_id)等
  - name: inventory
    desc: 商品SKU表，记录商品的详细信息，包括商品SKU(sku)、商品SPU ID(pd_id)、商品产地、商品规格(weight)等
  - name: category
    desc: 商品品类表，记录商品的类目信息，包括类目ID(id)、类目名字(category)、类目类型(type，=4表示水果类目)等
  - name: crm_bd_org
    desc: 销售组织架构表，记录销售人员的基本信息，包括销售人员ID(bd_id)、销售人员名字(bd_name)、销售人员所属的上级主管名字(parent_name)等
  - name: follow_up_relation
    desc: 商户销售私海关系表(reassign=0表示私海客户)，记录商户ID(m_id)、商户所属的销售员ID(admin_id)、商户所属的销售员名字(admin_name)等
  - name: follow_up_record
    desc: 记录商户被拜访的记录(有时也叫打卡记录），包括商户ID(m_id)、拜访人ID(admin_id)、拜访人名字(admin_name)、商户所属的运营服务区编码(area_no)等
  - name: after_sale_order
    desc: 售后单表，记录订单的售后申请的基本信息，包括售后订单编号(after_sale_order_no)、售后门店ID(m_id)、原订单编号(order_no)、售后商品SKU(sku)、售后状态(status)等
  - name: after_sale_proof
    desc: 售后明细表，记录售后单的处理明细情况，包括售后数量(quantity)、售后凭证图片(proof_pic)、最终售后金额(handle_num)、客服审核备注(apply_remark)等
  - name: delivery_plan
    desc: 订单配送计划表，记录订单的配送信息，包括配送时间(delivery_time)、配送状态(status)、配送商品数量(quantity)等
  - name: shopping_cart
    desc: 门店的购物车，记录门店的购物车信息，包括门店id(m_id)、子账号id(account_id)、商品sku(sku)、商品数量(quantity)等
  - name: products_property_value
    desc: 商品属性值表，主要用来记录商品的品牌信息，包括商品ID(pd_id)、商品品牌名称(products_property_value, 当且仅当products_property_id=2时，表示商品的品牌)
