"""
Model provider configuration for agent.
"""
import os
from openai import AsyncOpenAI
from src.utils.logger import logger
from agents import ModelProvider, Model, OpenAIChatCompletionsModel, set_tracing_disabled

# 基础模型配置
OPENAI_API_KEY = os.getenv("XM_OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE")
OPENAI_MODEL = os.getenv("OPENAI_MODEL")
OPENAI_MODEL_SETTINGS = os.getenv("OPENAI_MODEL_SETTINGS")

# 快模型配置
LITE_OPENAI_API_KEY = os.getenv("STRONG_XM_OPENAI_API_KEY", OPENAI_API_KEY)
LITE_OPENAI_API_BASE = os.getenv("LITE_OPENAI_API_BASE", OPENAI_API_BASE)
LITE_OPENAI_MODEL = os.getenv("LITE_OPENAI_MODEL", OPENAI_MODEL)
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

# Initialize OpenAI client
default_client = AsyncOpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)
strong_client = AsyncOpenAI(base_url=LITE_OPENAI_API_BASE, api_key=LITE_OPENAI_API_KEY)
set_tracing_disabled(disabled=True)

from agents.extensions.models.litellm_model import LitellmModel

# litellm的模型配置，需要以"openai/"开头
LITE_LLM_FAST_MODEL=LitellmModel(model=f"openai/{LITE_OPENAI_MODEL}", api_key=LITE_OPENAI_API_KEY, base_url=LITE_OPENAI_API_BASE)
LITE_LLM_MODEL=LitellmModel(model=f"openai/{OPENAI_MODEL}", api_key=LITE_OPENAI_API_KEY, base_url=LITE_OPENAI_API_BASE)


class CustomModelProvider(ModelProvider):
    """Custom model provider for OpenAI models."""

    def __init__(self, model_name: str = None, client: AsyncOpenAI = default_client):
        super().__init__()
        self.model_name = model_name
        self.client = client
    
    def get_model(self, model_name: str = None) -> Model:
        """
        Get the model to use for the agent.
        OPENAI_MODEL
        Args:
            model_name: Optional model name to use, defaults to OPENAI_MODEL from env
            
        Returns:
            Model: The model to use
        """
        logger.info(f"Using model: {model_name or self.model_name or OPENAI_MODEL}")
        return OpenAIChatCompletionsModel(
            model=model_name or self.model_name or OPENAI_MODEL, openai_client=self.client
        )


# Singleton instance for use throughout the application
CUSTOM_MODEL_PROVIDER = CustomModelProvider(client=default_client)
STRONG_MODEL_PROVIDER = CustomModelProvider(client=strong_client, model_name=LITE_OPENAI_MODEL)