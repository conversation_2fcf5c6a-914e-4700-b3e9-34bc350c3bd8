"""
Data fetcher bot implementation.
"""

import yaml
import textwrap
import json # Added import
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Callable

from agents import Agent, Model, ModelSettings

from src.models.user_info_class import UserInfo
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import OPENAI_MODEL, OPENAI_MODEL_SETTINGS
from src.services.agent.utils.permissions import get_user_permission
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.resource_manager import load_resource
from src.utils.logger import logger


def _load_config(config_file: str) -> Dict[str, Any]:
    """
    Load the agent configuration from a YAML file.

    Args:
        config_file: Name of the YAML configuration file

    Returns:
        Dict containing the configuration
    """
    # 使用 resource_manager 加载配置文件
    yaml_content = load_resource("data_fetcher_bot_config", config_file)

    if yaml_content:
        try:
            return yaml.safe_load(yaml_content)
        except Exception as e:
            # 如果 YAML 解析失败，记录错误并返回默认配置
            logger.error(f"Error parsing YAML config file {config_file}: {e}")
    else:
        logger.error(
            f"Config file {config_file} not found in data_fetcher_bot_config directory"
        )

    # 如果加载或解析失败，返回默认配置
    return {
        "agent_name": "sales_order_analytics",
        "agent_description": "销售订单分析专家",
        "agent_tables": [],
    }


class DataFetcherBot(BaseBot):
    """
    Data fetcher bot for retrieving data via SQL queries.

    This bot is specialized in:
    - Fetching DDL information for specific tables
    - Writing and executing SQL queries
    - Retrieving sample data from tables

    The bot can be configured with different YAML files to specialize in different domains.
    """

    def __init__(
        self, user_info: Dict[str, Any], config_file: str = "sales_orders.yml"
    ):
        super().__init__(user_info)
        self.config_file = config_file
        self.config = _load_config(config_file)
        self.table_with_desc = [
            f"- {t.get('name', 'N/A')}: {t.get('desc', 'N/A')}"
            for t in self.config.get("agent_tables", [])
        ]
        self.table_with_desc = "\n".join(self.table_with_desc)

    def get_description(self) -> str:
        agent_name = self.config.get("agent_name", "特定领域")
        agent_description = self.config.get("agent_description", "未提供描述")

        return f"我是一个专注于{agent_name}分析的专家，负责业务:\n{agent_description}\n我可以处理以下相关数据表: {self.table_with_desc}"

    def create_agent(self, model: Optional[str] = OPENAI_MODEL, model_settings_str: Optional[str] = OPENAI_MODEL_SETTINGS) -> Agent:
        date_time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        default_allowed_date = (datetime.now() - timedelta(days=31)).strftime(
            "%Y-%m-%d 00:00:00"
        )

        data_permission_markup = get_user_permission(self.user_info)
        agent_name = self.config.get("agent_name", "特定领域")
        agent_description = self.config.get("agent_description", "")
        tools = self.config.get("tools", [])
        logger.info(f"agent_name:{agent_name}, tools:{tools}")
        tool_list = tool_manager.get_tool_list([tool['name'] for tool in tools])

        system_instruction = load_resource("prompt", "data_fetcher_instruction.md")
        domain_instruction = textwrap.dedent(
            f"""
            你是一个专注于 {agent_name} 分析的专家。
            核心业务:
            {agent_description}
            相关数据表:
            {self.table_with_desc}
        """
        ).strip()

        # Create realtime instruction with user context
        realtime_instruction = textwrap.dedent(
            f"""
            请根据{self.user_name}的问题，使用工具查找相关表的DDL语句，然后参考DDL语句编写SQL来解答用户的问题。
            【非常重要，一定要遵守】用户{self.user_name}是重度中文用户，一点英语也不会，所以请始终用中文回答。
            当前时间: {date_time_of_now}
            当请求了orders表时，除非用户强烈要求，否则只需请求自{default_allowed_date}以来的数据。
            当用户提到'全品类'时，指的是`inventory`.`sub_type` in (1,2)的SKU(即代销不入仓或者代销入仓的商品)。
            当请求了merchant表时，除非用户明确指明查询异常状态的门店（如被拉黑，已注销，等），则必须筛选`merchant`.`islock` = 0的门店。
            【非常重要，一定要遵守】当你完成了SQL的编写后，一定要使用工具来为用户执行所编写的SQL，并返回执行结果。
            【非常重要，一定要遵守】数据权限申明：{data_permission_markup}。
        """
        ).strip()

        instruction = (
            f"{system_instruction}" f"{domain_instruction}" f"{realtime_instruction}"
        )

        model_settings = None
        if model_settings_str is not None and model_settings_str != "" and model_settings_str != "{}":
            try:
                model_settings_dict = json.loads(model_settings_str) # Parse JSON string
                model_settings = ModelSettings(**model_settings_dict) # Use parsed dict
            except json.JSONDecodeError as e:
                logger.error(f"Error decoding model_settings_str JSON: {e}")
            except TypeError as e:
                logger.error(f"Error creating ModelSettings: {e}. model_settings_dict was: {model_settings_dict if 'model_settings_dict' in locals() else 'not defined'}")
        
        if model_settings is None:
            agent = Agent[UserInfo](
                name=f"{agent_name}_specialist",
                instructions=instruction,
                model=model,
                tools=tool_list
            )
        else: 
            agent = Agent[UserInfo](
                name=f"{agent_name}_specialist",
                instructions=instruction,
                model=model,
                tools=tool_list,
                model_settings=model_settings
            )
        return agent
