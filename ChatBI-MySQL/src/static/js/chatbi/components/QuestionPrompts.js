/**
 * Question Prompts Component
 *
 * 显示预设问题提示的组件，结合了欢迎模块和模板按钮的功能
 * 遵循项目的Apple/OpenAI设计风格，提供优雅的用户体验
 */

import { computed } from 'vue';

export default {
    name: 'QuestionPrompts',
    props: {
        show: {
            type: Boolean,
            default: true
        },
        isHistoryTransition: {
            type: Boolean,
            default: false
        }
    },
    emits: ['question-click'],
    setup(props, { emit }) {
        // 预设问题列表
        const questions = [
            {
                id: 1,
                text: '我的客户中，过去30天购买了安佳淡奶油的有哪些？列出他们的详细信息。'
            },
            {
                id: 2,
                text: '杭州市过去7天新增了多少新注册的门店？'
            },
            {
                id: 3,
                text: '我的客户中，有哪些客户在过去6个月曾经购买过10单以上，但是最近30天都未再来下单的？列出他们的详细信息'
            },
            {
                id: 4,
                text: '杭州市过去30天下单数最大的客户是那几个？'
            },
            {
                id: 5,
                text: '门店ID=15062 的商户下单量最大的商品是哪些？列出他过去30天的下单商品详细信息'
            },
            {
                id: 6,
                text: '安佳淡奶油在全国各个仓库的库存情况是怎样的？'
            },
            {
                id: 7,
                text: '介绍下安佳淡奶油,可以用来做什么?'
            },
            {
                id: 8,
                text: '安佳淡奶油和铁塔淡奶油有什么区别？'
            },
            {
                id: 9,
                text: '607541303275 Protag纯牛奶的最新检测报告'
            },
            {
                id: 10,
                text: '查询澄善葡萄罐头的嘉兴库存信息、到货情况'
            }
        ];

        // 处理问题点击
        const handleQuestionClick = (question) => {
            emit('question-click', question);
        };

        // 根据上下文选择过渡动画名称
        const transitionName = computed(() => {
            return props.isHistoryTransition ? 'question-list-instant' : 'question-list';
        });

        return {
            questions,
            handleQuestionClick,
            transitionName
        };
    },
    template: `
        <!-- 问题列表区域 - 带过渡动画和延迟 -->
        <transition :name="transitionName" appear>
            <div v-if="show" class="question-prompts-container">
                <h1 class="question-prompts-main-title">欢迎使用ChatBI</h1>
                <p class="question-prompts-subtitle">您可以尝试询问以下问题，或直接输入您的问题</p>
                <div class="question-prompts-list scrollbar-auto">
                    <button
                        v-for="(question, index) in questions"
                        :key="question.id"
                        @click="handleQuestionClick(question)"
                        class="question-prompt-item"
                        :style="{ animationDelay: (index * 60 + 400) + 'ms' }"
                        :title="question.text"
                    >
                        <span class="question-prompt-text">{{ question.text }}</span>
                    </button>
                </div>
            </div>
        </transition>
    `
};
